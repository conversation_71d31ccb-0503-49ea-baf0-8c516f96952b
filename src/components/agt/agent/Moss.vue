<template>
  <div class="left-sidebar" id="MOSS">
    <!-- 左边栏 -->
    <div v-show="pageCtl.visible.sidebar">
      <moss-sidebar ref="mossSidebarRef"
                    :height="pageCtl.clientHeight"
                    :click-chat-log="showChatLog"
                    :click-hide-sidebar="toggleSidebar"
                    :click-new-chat="createNewChat"
                    :click-all-chat="viewAllChats"
                    :click-moss-plus="viewAllAgents"
                    :click-my-archive="clickMyArchive"
                    :current-container="pageCtl.visible.container"
      />
    </div>
    <!-- 右边栏 -->
    <!-- region: 右边栏 -->
    <div class="chat-container" :style="{height: pageCtl.clientHeight + 'px', width: pageCtl.sidebar? 'calc(100% - 240px)':'calc(100% - 10px)'}"
         v-loading="pageCtl.loading.agent">
      <moss-chat-log ref="mossChatLogRef" v-show="pageCtl.visible.container === 'CHAT_LOG'"
                     :height="pageCtl.clientHeight"
                     :click-show-sidebar="toggleSidebar"
                     :is-sidebar-btn-shown="pageCtl.visible.sidebar" />
      <moss-chat ref="mossChatRef" v-show="pageCtl.visible.container === 'CHAT'"
                 :key="pageCtl.chatKey"
                 :height="pageCtl.clientHeight"
                 :query-chat-logs="queryChatLogs"
                 :click-show-sidebar="toggleSidebar"
                 :is-sidebar-btn-shown="pageCtl.visible.sidebar" />
      <moss-all-chat v-show="pageCtl.visible.container === 'CHAT_ALL'"
                     :key="pageCtl.allChatKey"
                     :height="pageCtl.clientHeight"
                     :click-show-sidebar="toggleSidebar"
                     :click-view-chat="showChatLog"
                     :is-sidebar-btn-shown="pageCtl.visible.sidebar" />
      <moss-welcome ref="mossWelcomeRef"
                    v-show="pageCtl.visible.container === 'WELCOME_PAGE'"
                    :click-show-sidebar="toggleSidebar"
                    :height="pageCtl.clientHeight"
                    :click-view-chat="showChatLog"
                    :start-query="startQuery"
                    :is-sidebar-btn-shown="pageCtl.visible.sidebar" />
      <moss-agents v-show="pageCtl.visible.container === 'VIEW_AGENTS'"
                   :click-show-sidebar="toggleSidebar"
                   :height="pageCtl.clientHeight"
                   :is-sidebar-btn-shown="pageCtl.visible.sidebar"
                   :click-select-agent="selectAgent"
      />
      <div style="color: var(--scp-text-color-secondary);text-align: center;margin-top: -12px;font-size: 10px">内容由AI生成，请仔细甄别</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { inject, nextTick, onMounted, reactive, ref, watch } from 'vue'
import MossSidebar from '@/components/agt/agent/Moss/Sidebar.vue'
import MossChatLog from '@/components/agt/agent/Moss/ChatLog.vue'
import MossChat from '@/components/agt/agent/Moss/Chat.vue'
import MossAllChat from '@/components/agt/agent/Moss/AllChats.vue'
import MossWelcome from '@/components/agt/agent/Moss/Welcome.vue'
import MossAgents from '@/components/agt/agent/Moss/Agents.vue'
import { onBeforeRouteUpdate, useRoute } from 'vue-router'

const mossChatRef = ref()
const mossSidebarRef = ref()
const mossChatLogRef = ref()
const mossWelcomeRef = ref()
const $route = useRoute()
const $axios: any = inject('$axios')
const $randomString: any = inject('$randomString')

const pageCtl = reactive({
  chatKey: $randomString(8),
  allChatKey: $randomString(8),
  visible: {
    sidebar: true,
    container: 'WELCOME_PAGE'
  },
  loading: {
    agent: false
  },
  clientHeight: 300
})

const toggleSidebar = (state) => {
  pageCtl.visible.sidebar = state
}

const selectAgent = (item) => {
  pageCtl.visible.container = 'CHAT'
  mossChatRef.value.selectAgent(item)
}

const clickMyArchive = (item) => {
  mossWelcomeRef.value.setQueryMsg(item)
}

const createNewChat = () => {
  pageCtl.visible.container = 'WELCOME_PAGE'
  pageCtl.chatKey = $randomString(8) // 重新渲染组件
  setTimeout(() => queryChatLogs(), 2000)
}

const startQuery = (params) => {
  pageCtl.visible.container = 'CHAT'
  mossChatRef.value.setQueryParams(params)
  nextTick(() => {
    mossChatRef.value.searchAgentId()
  })
}

const viewAllChats = () => {
  pageCtl.visible.container = 'CHAT_ALL'
  pageCtl.allChatKey = $randomString(8) // 重新渲染组件
}

const viewAllAgents = () => {
  pageCtl.visible.container = 'VIEW_AGENTS'
}

const showChatLog = (item) => {
  pageCtl.visible.container = 'CHAT_LOG'
  mossChatLogRef.value.showChatLog(item.ID)
}

const queryChatLogs = () => {
  mossSidebarRef.value.queryChatLogs()
}

const adjustHeight = () => {
  pageCtl.clientHeight = document.documentElement.clientHeight - 70
}

window.addEventListener('resize', () => {
  adjustHeight()
})

watch(() => $route.query.aid, () => {
  loadPage($route.query.aid)
})

const loadPage = (id?) => {
  const aid = id || $route.query.aid
  if (aid) {
    pageCtl.loading.agent = true
    $axios({
      method: 'post',
      url: '/intelligent_agent/moss/query_agent_by_id',
      data: {
        aid
      }
    }).then((body) => {
      if (body) {
        pageCtl.visible.container = 'CHAT'
        mossChatRef.value.selectAgent(body)
      }
    }).finally(() => {
      pageCtl.loading.agent = false
    })
  }
}

onBeforeRouteUpdate(() => {
  loadPage()
})

onMounted(() => {
  adjustHeight()
  loadPage()
})
</script>

<style lang="scss">
#MOSS {
  background-color: #f3f5f6;
  width: calc(100% - 10px) !important;
  display: flex;
  padding: 5px;

  .chat-container {
    background-color: #fff;
    padding: 5px;
    border-radius: 8px;
    box-shadow: 0 0 1px 1px #efefef;
  }
}

</style>
