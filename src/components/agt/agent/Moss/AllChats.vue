<template>
  <div id="MOSSAllChat" :style="{height: height + 'px'}">
    <div class="chat-title">
      <!-- region: 右边栏, 打开关闭侧边栏 -->
      <span style="cursor: pointer" @click="clickShowSidebar(true)" :style="{color:isSidebarBtnShown? 'transparent':'inherit'}">
          <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" data-v-f2dda92e="" style="" width="20"
               height="20" viewBox="0 0 1024 1024" class="iconify"><path
            d="M861.866667 162.133333c-17.066667-17.066667-42.666667-29.866667-68.266667-29.866666H226.133333c-25.6 0-51.2 8.533333-68.266666 29.866666S128 204.8 128 230.4v567.466667c0 25.6 8.533333 51.2 29.866667 68.266666 17.066667 17.066667 42.666667 29.866667 68.266666 29.866667h567.466667c25.6 0 51.2-8.533333 68.266667-29.866667 17.066667-17.066667 29.866667-42.666667 29.866666-68.266666V226.133333c0-25.6-8.533333-46.933333-29.866666-64zM366.933333 814.933333H226.133333c-4.266667 0-8.533333 0-12.8-4.266666-4.266667-4.266667-4.266667-8.533333-4.266666-12.8V226.133333c0-4.266667 0-8.533333 4.266666-12.8 4.266667-4.266667 8.533333-4.266667 12.8-4.266666h140.8v605.866666z m448-17.066666c0 4.266667 0 8.533333-4.266666 12.8-4.266667 4.266667-8.533333 4.266667-12.8 4.266666h-354.133334V209.066667h354.133334c4.266667 0 8.533333 0 12.8 4.266666 4.266667 4.266667 4.266667 8.533333 4.266666 12.8v571.733334z"
            fill="currentColor"></path></svg>
          &nbsp;&nbsp;
        </span>
      <b>Chat History</b>
      <!-- endregion -->
    </div>
    <!--  主功能区域 -->
    <div class="chat-container" v-loading="pageCtl.loading.search">
      <el-input class="search-input" v-model="pageCtl.searchInput" size="large" placeholder="Search chat history" @keydown.enter="enterSearch" clearable>
        <template #prepend>
          <font-awesome-icon icon="search" />
        </template>
      </el-input>

      <ul v-infinite-scroll="search" class="chat-infinite-list" :style="{height: (height - 120) + 'px'}">
        <li v-for="item in pageCtl.chatHistory" :key="item.ID" style="margin-bottom: 10px" @dblclick="()=>clickViewChat(item)">
          <div style="padding: 16px;width: 75%;margin: 0 auto" class="infinite-list-item">
            <el-row>
              <el-col :span="20" class="item-title"><div v-html="item.SUBJECT"/></el-col>
              <el-col :span="4" class="item-date">{{ item.CREATE_DATE }}</el-col>
            </el-row>
            <div class="item-content" v-html="item.LOGS"/>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, onMounted, reactive } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

const $axios: any = inject('$axios')

const pageCtl = reactive({
  searchInput: '',
  chatHistory: [],
  loading: {
    search: false
  }
})

const props = withDefaults(
  defineProps<{
    clickShowSidebar?: Function,
    clickViewChat?: Function,
    isSidebarBtnShown?: boolean,
    height?: number
  }>(), {
    clickShowSidebar: undefined,
    clickViewChat: undefined,
    isSidebarBtnShown: false,
    height: 300
  }
)

const enterSearch = () => {
  pageCtl.loading.search = true
  pageCtl.chatHistory = []
  $axios({
    method: 'post',
    url: '/intelligent_agent/moss/query_all_chat_logs',
    data: {
      offset: pageCtl.chatHistory.length,
      keyword: pageCtl.searchInput
    }
  }).then((body) => {
    pageCtl.chatHistory.push(...body)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.search = false
  })
}

const search = () => {
  if (pageCtl.loading.search) {
    return
  }
  pageCtl.loading.search = true
  $axios({
    method: 'post',
    url: '/intelligent_agent/moss/query_all_chat_logs',
    data: {
      offset: pageCtl.chatHistory.length,
      keyword: pageCtl.searchInput
    }
  }).then((body) => {
    pageCtl.chatHistory.push(...body)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.search = false
  })
}

onMounted(() => {
  search()
})

</script>

<script lang="ts">
export default {
  name: 'MossAllChat'
}
</script>

<style lang="scss">
#MOSSAllChat {
  width: 100%;
  display: flex;
  flex-direction: column;

  .chat-title {
    font-size: 0.6rem;
    padding: 12px 5px 0 5px;
    display: flex;
    align-items: flex-start;
    height: 26px;
  }

  .chat-container {
    display: flex;
    box-shadow: none !important;
    flex-direction: column;
    align-items: center;
    width: calc(100% - 5px);
    margin: 10px auto;

    .search-input {
      max-width: 50%;
      min-width: 400px;
      margin: 0 auto;

      .el-input-group__prepend {
        border-radius: 4px 0 0 4px;
        background-color: #f5f5f5 !important;
        box-shadow: none;
      }

      .el-input__wrapper {
        border-radius: 0 4px 4px 0;
        background-color: #f5f5f5 !important;
        box-shadow: none;
      }
    }

    .chat-infinite-list {
      overflow: auto;
      margin-top: 12px;
      width: 100%;

      .infinite-list-item {
        max-height: 85px;
        border-radius: 8px;

        .item-title {
          font-weight: bold;
          font-size: 0.55rem;
        }

        .item-date {
          text-align: right;
          color: var(--scp-text-color-secondary);
          font-size: 0.45rem;
        }

        .item-content {
          color: var(--scp-text-color-secondary);
          overflow: hidden;
          max-height: 62px;
          margin-top: 5px;
        }
      }

      .infinite-list-item:hover {
        background-color: #f5f5f5 !important;
        cursor: pointer;
      }
    }
  }
}
</style>
